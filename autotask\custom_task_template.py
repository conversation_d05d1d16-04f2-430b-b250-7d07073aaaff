import asyncio
import os
import sys

# browser-use 0.4.5版本直接从已安装的库导入，不需要添加路径

from dotenv import load_dotenv

load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatGoogle

# 检查API密钥
api_key = os.getenv('GOOGLE_API_KEY')
if not api_key:
    raise ValueError('GOOGLE_API_KEY is not set. Please set your Google API key.')

# 初始化Gemini模型
llm = ChatGoogle(model='gemini-2.0-flash-exp', api_key=api_key)

# 创建浏览器会话（使用stealth模式）
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        viewport_expansion=0,
        user_data_dir='E:\\newchrome\\网页缓存10',  # 🔧 修改为您想要的浏览器编号
        stealth=True,  # 启用stealth模式
        headless=False,  # 显示浏览器窗口
    )
)

async def run_custom_task():
    """执行自定义任务"""
    
    print("🎯 开始执行自定义任务...")
    
    # 🔧 在这里修改您的任务描述
    task_description = """
    请执行以下任务：
    
    1. 访问 https://www.google.com 网站
    2. 在搜索框中输入 "browser-use github"
    3. 点击搜索按钮
    4. 点击第一个搜索结果
    5. 在GitHub页面上点击Star按钮
    6. 截图保存结果
    
    注意：
    - 请仔细观察页面内容
    - 如果需要登录，请告诉我
    - 操作要自然，不要太快
    """
    
    agent = Agent(
        task=task_description,
        llm=llm,
        max_actions_per_step=4,
        browser_session=browser_session,
    )
    
    print("🤖 AI代理开始执行任务...")
    result = await agent.run(max_steps=25)
    
    print("✅ 自定义任务执行完成!")
    print(f"执行结果: {result}")

# 全局任务控制变量
_task_cancelled = False

def cancel_task():
    """取消当前任务"""
    global _task_cancelled
    _task_cancelled = True
    print("🛑 任务取消信号已发送")

async def _check_cancellation():
    """定期检查取消标志"""
    global _task_cancelled
    while not _task_cancelled:
        await asyncio.sleep(0.5)  # 每0.5秒检查一次
    raise asyncio.CancelledError("Task cancelled by user")

# Chrome管理器集成函数
async def run_stealth_task(browser_session, browser_num):
    """
    Chrome管理器调用的stealth任务函数

    Args:
        browser_session: browser-use的BrowserSession对象
        browser_num: 浏览器编号
    """
    global _task_cancelled
    _task_cancelled = False  # 重置取消标志

    try:
        print(f"🎯 浏览器 {browser_num} 开始执行自定义任务...")

        # 检查是否已取消
        if _task_cancelled:
            print(f"🛑 浏览器 {browser_num} 任务已被取消")
            return

        # 🔧 在这里修改您的任务描述
        task_description = """
        请执行以下任务：

        1. 访问 https://www.google.com 网站
        2. 在搜索框中输入 "browser-use github"
        3. 点击搜索按钮
        4. 点击第一个搜索结果
        5. 在GitHub页面上点击Star按钮
        6. 截图保存结果

        注意：
        - 请仔细观察页面内容
        - 如果需要登录，请告诉我
        - 操作要自然，不要太快
        """

        agent = Agent(
            task=task_description,
            llm=llm,
            max_actions_per_step=4,
            browser_session=browser_session,
        )

        print(f"🤖 浏览器 {browser_num} AI代理开始执行任务...")

        try:
            # 创建任务
            agent_task = asyncio.create_task(agent.run(max_steps=15))

            # 创建取消检查任务
            cancel_check_task = asyncio.create_task(_check_cancellation())

            # 等待任务完成或取消
            done, pending = await asyncio.wait(
                [agent_task, cancel_check_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            # 检查结果
            if agent_task in done:
                result = await agent_task
                print(f"✅ 浏览器 {browser_num} 自定义任务执行完成!")
                print(f"执行结果: {result}")
            else:
                print(f"🛑 浏览器 {browser_num} 任务被用户取消")

        except asyncio.CancelledError:
            print(f"🛑 浏览器 {browser_num} 任务被取消")

    except Exception as e:
        print(f"❌ 浏览器 {browser_num} 自定义任务失败: {e}")
        import traceback
        traceback.print_exc()

# 兼容性函数
async def run_automation_task(browser_session, browser_num):
    """兼容性函数，调用主要的stealth任务"""
    await run_stealth_task(browser_session, browser_num)

if __name__ == '__main__':
    print("🚀 启动自定义任务自动化脚本...")
    print("📋 任务: 自定义网站自动化操作")
    print("🛡️ 模式: Stealth模式 (防检测)")
    print("🤖 AI模型: Gemini 2.0 Flash")
    print("-" * 50)
    
    asyncio.run(run_custom_task())

"""
使用说明：

1. 设置API密钥：
   set GOOGLE_API_KEY=your_api_key_here

2. 修改任务：
   - 编辑 task_description 变量
   - 修改 user_data_dir 为您想要的浏览器缓存目录
   - 调整 max_steps 和 max_actions_per_step 参数

3. 运行脚本：
   cd autotask
   python custom_task_template.py

4. 任务描述示例：
   - "访问Google搜索Python教程，点击第一个结果"
   - "登录Twitter并发布一条推文：Hello World"
   - "在Amazon搜索iPhone，查看第一个商品详情"
   - "访问GitHub，搜索browser-use项目并给它点星"

5. 高级配置：
   - model: 可以改为 'gemini-1.5-flash' 或 'gemini-1.5-pro'
   - headless: 设为 True 可以无头模式运行
   - max_steps: 控制AI最大执行步数
   - max_actions_per_step: 控制每步最大操作数
"""
