import asyncio
import os
import sys

# browser-use 0.4.5版本直接从已安装的库导入，不需要添加路径

from dotenv import load_dotenv

load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatGoogle

# 检查API密钥
api_key = os.getenv('GOOGLE_API_KEY')
if not api_key:
    raise ValueError('GOOGLE_API_KEY is not set. Please set your Google API key.')

# 初始化Gemini模型
llm = ChatGoogle(model='gemini-2.0-flash-exp', api_key=api_key)

# 创建浏览器会话
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        viewport_expansion=0,
        user_data_dir='E:\\newchrome\\网页缓存10',
        stealth=True,
        headless=False,
    )
)

async def run_bot_detection_test():
    """执行机器人检测测试"""
    
    print("🤖 开始执行机器人检测测试...")
    
    agent = Agent(
        task="""
        请执行以下机器人检测测试：
        
        1. 访问 https://bot-detector.rebrowser.net/
        2. 等待页面完全加载
        3. 观察页面上的各项检测结果
        4. 查看每个检测项目的状态（通过/失败）
        5. 注意整体的检测评分
        6. 如果有交互按钮，可以点击测试
        7. 截图保存完整的检测结果页面
        
        重点关注：
        - 各项检测的通过率
        - 是否被识别为机器人
        - WebDriver检测结果
        - 鼠标和键盘行为检测
        - 整体评估结果
        
        注意：
        - 让所有检测项目完全加载
        - 自然地移动鼠标和滚动页面
        - 不要过快操作
        """,
        llm=llm,
        max_actions_per_step=3,
        browser_session=browser_session,
    )
    
    print("🤖 AI代理开始执行机器人检测...")
    result = await agent.run(max_steps=20)
    
    print("✅ 机器人检测测试完成!")
    print(f"执行结果: {result}")

if __name__ == '__main__':
    print("🚀 启动机器人检测测试...")
    print("📋 任务: Bot Detection分析")
    print("🛡️ 模式: Stealth模式 (防检测)")
    print("🤖 AI模型: Gemini 2.0 Flash")
    print("-" * 50)
    
    asyncio.run(run_bot_detection_test())
