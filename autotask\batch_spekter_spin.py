import asyncio
import os
import sys

# browser-use 0.4.5版本直接从已安装的库导入，不需要添加路径

from dotenv import load_dotenv

load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatGoogle

# 检查API密钥
api_key = os.getenv('GOOGLE_API_KEY')
if not api_key:
    raise ValueError('GOOGLE_API_KEY is not set. Please set your Google API key.')

# 配置参数
BROWSER_NUMBERS = [1, 2, 3, 4, 5]  # 🔧 修改为您想要使用的浏览器编号
CACHE_BASE_DIR = "E:\\newchrome"   # 🔧 修改为您的缓存基础目录
MAX_CONCURRENT = 3                 # 🔧 最大并发数，避免过载

# 初始化Gemini模型
llm = ChatGoogle(model='gemini-2.0-flash-exp', api_key=api_key)

async def run_spekter_for_browser(browser_num):
    """为指定浏览器执行Spekter任务"""
    
    print(f"🎮 浏览器 {browser_num}: 开始执行Spekter任务...")
    
    # 创建独立的浏览器会话
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            viewport_expansion=0,
            user_data_dir=f'{CACHE_BASE_DIR}\\网页缓存{browser_num}',
            stealth=True,
            headless=False,
        )
    )
    
    try:
        agent = Agent(
            task=f"""
            请为浏览器 {browser_num} 执行Spekter游戏任务：
            
            1. 访问 https://dispatch.spekter.games/spin
            2. 等待页面完全加载
            3. 找到并点击Spin按钮
            4. 等待游戏结果（30秒）
            5. 截图保存为 spekter_result_browser_{browser_num}.png
            
            注意：这是浏览器 {browser_num}，请确保操作成功完成。
            """,
            llm=llm,
            max_actions_per_step=4,
            browser_session=browser_session,
        )
        
        print(f"🤖 浏览器 {browser_num}: AI代理开始执行...")
        result = await agent.run(max_steps=20)
        
        print(f"✅ 浏览器 {browser_num}: 任务执行完成!")
        return f"浏览器 {browser_num}: 成功 - {result}"
        
    except Exception as e:
        print(f"❌ 浏览器 {browser_num}: 任务失败 - {e}")
        return f"浏览器 {browser_num}: 失败 - {str(e)}"
        
    finally:
        try:
            await browser_session.close()
            print(f"🔒 浏览器 {browser_num}: 会话已关闭")
        except:
            pass

async def run_batch_tasks():
    """批量执行任务"""
    
    print(f"🚀 开始批量执行Spekter任务...")
    print(f"📋 目标浏览器: {BROWSER_NUMBERS}")
    print(f"🔄 最大并发数: {MAX_CONCURRENT}")
    print("-" * 50)
    
    # 使用信号量控制并发数
    semaphore = asyncio.Semaphore(MAX_CONCURRENT)
    
    async def run_with_semaphore(browser_num):
        async with semaphore:
            return await run_spekter_for_browser(browser_num)
    
    # 并发执行所有任务
    tasks = [run_with_semaphore(num) for num in BROWSER_NUMBERS]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 输出结果汇总
    print("\n" + "=" * 50)
    print("📊 任务执行结果汇总:")
    print("=" * 50)
    
    success_count = 0
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"❌ 浏览器 {BROWSER_NUMBERS[i]}: 异常 - {result}")
        else:
            print(f"📋 {result}")
            if "成功" in str(result):
                success_count += 1
    
    print(f"\n✅ 成功: {success_count}/{len(BROWSER_NUMBERS)}")
    print(f"❌ 失败: {len(BROWSER_NUMBERS) - success_count}/{len(BROWSER_NUMBERS)}")

if __name__ == '__main__':
    print("🎯 Spekter游戏批量自动化脚本")
    print("🛡️ 模式: Stealth模式 (防检测)")
    print("🤖 AI模型: Gemini 2.0 Flash")
    print("⚡ 支持并发执行")
    print("-" * 50)
    
    asyncio.run(run_batch_tasks())

"""
使用说明：

1. 配置参数：
   - BROWSER_NUMBERS: 修改为您要使用的浏览器编号列表
   - CACHE_BASE_DIR: 修改为您的缓存目录
   - MAX_CONCURRENT: 调整并发数（建议不超过5）

2. 设置API密钥：
   set GOOGLE_API_KEY=your_api_key_here

3. 运行脚本：
   cd autotask
   python batch_spekter_spin.py

4. 监控执行：
   - 脚本会显示每个浏览器的执行状态
   - 最后会显示成功/失败统计
   - 每个浏览器会保存独立的截图
"""
