"""
自定义任务模板 - 请复制此文件并修改为您的任务
使用browser-use stealth模式执行
"""
import asyncio

async def run_stealth_task(browser_session, browser_num):
    """
    使用stealth模式运行自定义任务
    
    Args:
        browser_session: browser-use的BrowserSession对象
        browser_num: 浏览器编号
    """
    try:
        # 1. 设置目标网址
        target_url = "https://example.com"  # 修改为您的目标网址
        print(f"浏览器 {browser_num} 开始自定义任务: {target_url}")
        
        # 2. 导航到目标页面
        await browser_session.create_new_tab(target_url)
        print(f"浏览器 {browser_num} 已导航到目标页面")
        
        # 3. 等待页面加载
        await asyncio.sleep(5)
        
        # 4. 获取当前页面
        page = await browser_session.get_current_page()
        
        # 5. 执行您的自定义操作
        print(f"浏览器 {browser_num} 开始执行自定义操作...")
        
        # 示例：点击按钮
        try:
            # 修改选择器为您需要的元素
            button_selectors = [
                'button:has-text("点击")',
                'button:has-text("Click")',
                '.my-button',
                '#my-button'
            ]
            
            clicked = False
            for selector in button_selectors:
                try:
                    print(f"浏览器 {browser_num} 尝试点击: {selector}")
                    await page.click(selector, timeout=3000)
                    print(f"浏览器 {browser_num} 成功点击按钮")
                    clicked = True
                    break
                except Exception as e:
                    print(f"浏览器 {browser_num} 选择器 {selector} 失败: {e}")
                    continue
            
            if not clicked:
                print(f"浏览器 {browser_num} 未找到目标按钮")
        
        except Exception as e:
            print(f"浏览器 {browser_num} 执行操作失败: {e}")
        
        # 示例：填写表单
        try:
            # 查找输入框并填写内容
            input_selector = 'input[type="text"]'  # 修改为您的输入框选择器
            await page.fill(input_selector, "您的文本内容")
            print(f"浏览器 {browser_num} 已填写表单")
        except Exception as e:
            print(f"浏览器 {browser_num} 填写表单失败: {e}")
        
        # 6. 等待操作完成
        wait_time = 10  # 修改等待时间（秒）
        print(f"浏览器 {browser_num} 等待{wait_time}秒...")
        await asyncio.sleep(wait_time)
        
        # 7. 保存截图
        screenshot_path = f"custom_task_browser_{browser_num}.png"
        await page.screenshot(path=screenshot_path)
        print(f"浏览器 {browser_num} 任务截图已保存: {screenshot_path}")
        
        print(f"浏览器 {browser_num} 自定义任务执行完成")
        
    except Exception as e:
        print(f"浏览器 {browser_num} 自定义任务失败: {e}")
        import traceback
        traceback.print_exc()

# 兼容性函数（如果需要的话）
async def run_automation_task(browser_session, browser_num):
    """兼容性函数，调用主要的stealth任务"""
    await run_stealth_task(browser_session, browser_num)

"""
使用说明：
1. 复制此文件并重命名为您的任务名称（如：my_game_task.py）
2. 修改 target_url 为您的目标网址
3. 修改选择器和操作逻辑
4. 调整等待时间
5. 保存文件到 autotask 目录
6. 在Chrome管理器中点击"刷新"按钮
7. 选择您的任务并执行

常用选择器：
- 按钮：'button', 'input[type="button"]', '.btn'
- 输入框：'input[type="text"]', 'input[name="username"]'
- 链接：'a[href*="keyword"]', 'a:has-text("链接文本")'
- 类名：'.class-name'
- ID：'#element-id'
- 属性：'[data-testid="test"]'

常用操作：
- 点击：await page.click(selector)
- 填写：await page.fill(selector, "文本")
- 等待：await asyncio.sleep(秒数)
- 截图：await page.screenshot(path="文件名.png")
- 滚动：await page.mouse.wheel(0, 500)
"""
