#!/usr/bin/env python3
"""
自动化任务启动器
提供交互式菜单来选择和运行任务
"""
import os
import sys
import subprocess
import glob

def get_available_tasks():
    """获取可用的任务列表"""
    task_files = glob.glob("*.py")
    # 排除启动器和模板文件
    exclude_files = ["run_task.py", "__init__.py"]
    
    tasks = []
    for file in task_files:
        if file not in exclude_files:
            # 读取文件的第一行注释作为描述
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    description = file.replace('.py', '').replace('_', ' ').title()
                    
                    # 尝试从文件中提取描述
                    for line in lines[:10]:
                        if '"""' in line or "'''" in line:
                            continue
                        if line.strip().startswith('#') and len(line.strip()) > 2:
                            description = line.strip()[1:].strip()
                            break
                    
                    tasks.append((file, description))
            except:
                tasks.append((file, file.replace('.py', '').replace('_', ' ').title()))
    
    return sorted(tasks)

def check_api_key():
    """检查API密钥是否设置"""
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ 错误: 未设置 GOOGLE_API_KEY 环境变量")
        print("\n请先设置您的Google API密钥:")
        print("Windows: set GOOGLE_API_KEY=your_api_key_here")
        print("Linux/Mac: export GOOGLE_API_KEY=your_api_key_here")
        print("\n获取API密钥: https://makersuite.google.com/app/apikey")
        return False
    
    print(f"✅ API密钥已设置: {api_key[:10]}...")
    return True

def show_menu():
    """显示任务菜单"""
    print("\n" + "=" * 60)
    print("🤖 自动化任务启动器")
    print("🛡️ 使用browser-use + Gemini AI + Stealth模式")
    print("=" * 60)
    
    if not check_api_key():
        return
    
    tasks = get_available_tasks()
    
    if not tasks:
        print("❌ 未找到任何任务文件")
        return
    
    print("\n📋 可用任务:")
    print("-" * 40)
    
    for i, (filename, description) in enumerate(tasks, 1):
        print(f"{i:2d}. {description}")
        print(f"    文件: {filename}")
        print()
    
    print(f"{len(tasks) + 1:2d}. 退出")
    print("-" * 40)
    
    try:
        choice = input("\n请选择要执行的任务 (输入数字): ").strip()
        
        if not choice.isdigit():
            print("❌ 请输入有效的数字")
            return
        
        choice = int(choice)
        
        if choice == len(tasks) + 1:
            print("👋 再见!")
            return
        
        if 1 <= choice <= len(tasks):
            filename = tasks[choice - 1][0]
            description = tasks[choice - 1][1]
            
            print(f"\n🚀 启动任务: {description}")
            print(f"📁 文件: {filename}")
            print("-" * 40)
            
            # 运行选择的任务
            try:
                subprocess.run([sys.executable, filename], check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ 任务执行失败: {e}")
            except KeyboardInterrupt:
                print("\n⚠️ 任务被用户中断")
            
        else:
            print("❌ 无效的选择")
            
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    # 确保在正确的目录中
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    while True:
        show_menu()
        
        try:
            again = input("\n是否继续运行其他任务? (y/n): ").strip().lower()
            if again not in ['y', 'yes', '是', '']:
                break
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break

if __name__ == '__main__':
    main()
