"""
AI Agent任务模板 - 使用browser-use + Gemini API
支持自然语言描述任务，让AI自动执行
"""
import asyncio
import os

async def run_stealth_task(browser_session, browser_num):
    """
    使用AI Agent执行任务
    
    Args:
        browser_session: browser-use的BrowserSession对象
        browser_num: 浏览器编号
    """
    try:
        # 检查是否安装了必要的依赖
        try:
            from browser_use import Agent
            from browser_use.llm import ChatGoogle  # Gemini API
        except ImportError as e:
            print(f"缺少必要的依赖: {e}")
            print("请确保安装了browser-use和相关依赖")
            return
        
        print(f"浏览器 {browser_num} 开始AI Agent任务")
        
        # 1. 配置Gemini API
        # 您需要设置环境变量或直接提供API密钥
        gemini_api_key = os.getenv('GEMINI_API_KEY')  # 从环境变量获取
        
        if not gemini_api_key:
            # 如果没有环境变量，您可以直接在这里设置（不推荐用于生产环境）
            gemini_api_key = "YOUR_GEMINI_API_KEY_HERE"  # 替换为您的API密钥
            
        if not gemini_api_key or gemini_api_key == "YOUR_GEMINI_API_KEY_HERE":
            print("错误: 未设置Gemini API密钥")
            print("请设置环境变量 GEMINI_API_KEY 或在代码中直接设置")
            return
        
        # 2. 创建Gemini LLM实例
        llm = ChatGoogle(
            model="gemini-1.5-flash",  # 或使用 "gemini-1.5-pro"
            api_key=gemini_api_key,
            temperature=0.1  # 降低随机性，提高任务执行的一致性
        )
        
        # 3. 定义任务描述
        # 修改这里的任务描述为您想要执行的具体任务
        task_description = """
        请访问 https://dispatch.spekter.games/spin 网站，
        找到并点击 "spin" 按钮，
        等待30秒观察结果，
        然后截图保存。
        """
        
        print(f"浏览器 {browser_num} 任务描述: {task_description}")
        
        # 4. 创建AI Agent
        agent = Agent(
            task=task_description,
            llm=llm,
            browser_session=browser_session,
            # 可选配置
            max_actions=20,  # 最大操作步数
            use_vision=True,  # 启用视觉理解
        )
        
        print(f"浏览器 {browser_num} AI Agent已创建，开始执行任务...")
        
        # 5. 执行任务
        result = await agent.run()
        
        print(f"浏览器 {browser_num} AI Agent任务执行完成")
        print(f"执行结果: {result}")
        
        # 6. 保存最终截图
        try:
            page = await browser_session.get_current_page()
            screenshot_path = f"ai_agent_result_browser_{browser_num}.png"
            await page.screenshot(path=screenshot_path)
            print(f"浏览器 {browser_num} 最终截图已保存: {screenshot_path}")
        except Exception as e:
            print(f"保存截图失败: {e}")
        
    except Exception as e:
        print(f"浏览器 {browser_num} AI Agent任务失败: {e}")
        import traceback
        traceback.print_exc()

# 兼容性函数
async def run_automation_task(browser_session, browser_num):
    """兼容性函数，调用主要的stealth任务"""
    await run_stealth_task(browser_session, browser_num)

"""
使用说明：

1. 设置Gemini API密钥：
   方法1（推荐）：设置环境变量
   - Windows: set GEMINI_API_KEY=your_api_key_here
   - Linux/Mac: export GEMINI_API_KEY=your_api_key_here
   
   方法2：直接在代码中设置
   - 修改上面的 gemini_api_key = "YOUR_GEMINI_API_KEY_HERE"

2. 修改任务描述：
   - 编辑 task_description 变量
   - 使用自然语言描述您想要执行的任务
   - AI会根据描述自动执行相应操作

3. 任务描述示例：
   - "访问Google搜索Python教程"
   - "登录Twitter并发布一条推文"
   - "在Amazon搜索iPhone并查看第一个商品"
   - "填写联系表单并提交"

4. 高级配置：
   - max_actions: 限制AI执行的最大操作数
   - temperature: 控制AI的随机性（0-1，越低越确定）
   - use_vision: 是否启用视觉理解功能

注意事项：
- 确保网络连接稳定
- 某些网站可能有反爬虫机制
- 复杂任务可能需要多次尝试
- 保护好您的API密钥，不要泄露
"""
