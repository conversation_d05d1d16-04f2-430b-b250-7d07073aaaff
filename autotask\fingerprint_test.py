import asyncio
import os
import sys

# browser-use 0.4.5版本直接从已安装的库导入，不需要添加路径

from dotenv import load_dotenv

load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatGoogle

# 检查API密钥
api_key = os.getenv('GOOGLE_API_KEY')
if not api_key:
    raise ValueError('GOOGLE_API_KEY is not set. Please set your Google API key.')

# 初始化Gemini模型
llm = ChatGoogle(model='gemini-2.0-flash-exp', api_key=api_key)

# 创建浏览器会话
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        viewport_expansion=0,
        user_data_dir='E:\\newchrome\\网页缓存10',
        stealth=True,
        headless=False,
    )
)

async def run_fingerprint_test():
    """执行浏览器指纹检测测试"""
    
    print("🔍 开始执行浏览器指纹检测测试...")
    
    agent = Agent(
        task="""
        请执行以下浏览器指纹检测测试：
        
        1. 访问 https://abrahamjuliot.github.io/creepjs/
        2. 等待页面完全加载（可能需要10-15秒）
        3. 观察页面上的检测结果
        4. 查看Trust Score（信任分数）
        5. 检查是否有红色警告标记
        6. 滚动页面查看详细的检测项目
        7. 截图保存完整的检测结果
        
        重点关注：
        - Trust Score分数（越高越好）
        - 是否有"Bot Detection"警告
        - Canvas、WebGL、Audio等指纹项目
        - 整体的伪装效果评估
        
        注意：
        - 页面加载可能较慢，请耐心等待
        - 不要过快操作，让检测完全完成
        """,
        llm=llm,
        max_actions_per_step=3,
        browser_session=browser_session,
    )
    
    print("🤖 AI代理开始执行指纹检测...")
    result = await agent.run(max_steps=20)
    
    print("✅ 指纹检测测试完成!")
    print(f"执行结果: {result}")

if __name__ == '__main__':
    print("🚀 启动浏览器指纹检测测试...")
    print("📋 任务: CreepJS指纹检测分析")
    print("🛡️ 模式: Stealth模式 (防检测)")
    print("🤖 AI模型: Gemini 2.0 Flash")
    print("-" * 50)
    
    asyncio.run(run_fingerprint_test())
