"""
Spekter游戏AI Spin任务 - 使用Gemini AI自动执行
让AI智能识别页面并执行Spin操作
"""
import asyncio
import os

async def run_stealth_task(browser_session, browser_num):
    """
    使用AI Agent执行Spekter游戏Spin任务
    
    Args:
        browser_session: browser-use的BrowserSession对象
        browser_num: 浏览器编号
    """
    try:
        # 检查依赖
        try:
            from browser_use import Agent
            from browser_use.llm import ChatGoogle
        except ImportError as e:
            print(f"缺少browser-use依赖: {e}")
            print("请安装: pip install browser-use")
            return
        
        print(f"浏览器 {browser_num} 开始Spekter AI Spin任务")
        
        # 获取Gemini API密钥
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not gemini_api_key:
            print("警告: 未设置GEMINI_API_KEY环境变量")
            print("请设置您的Gemini API密钥:")
            print("Windows: set GEMINI_API_KEY=your_api_key")
            print("Linux/Mac: export GEMINI_API_KEY=your_api_key")
            
            # 临时解决方案：从用户输入获取（仅用于测试）
            try:
                from tkinter import simpledialog
                import tkinter as tk
                
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                
                gemini_api_key = simpledialog.askstring(
                    "Gemini API密钥",
                    "请输入您的Gemini API密钥:",
                    show='*'  # 隐藏输入内容
                )
                root.destroy()
                
                if not gemini_api_key:
                    print("未提供API密钥，任务取消")
                    return
                    
            except Exception as e:
                print(f"无法获取API密钥: {e}")
                return
        
        # 创建Gemini LLM
        llm = ChatGoogle(
            model="gemini-1.5-flash",
            api_key=gemini_api_key,
            temperature=0.1
        )
        
        # 定义Spekter游戏任务
        task_description = """
        请执行以下Spekter游戏任务：
        
        1. 访问 https://dispatch.spekter.games/spin 网站
        2. 等待页面完全加载
        3. 仔细观察页面，找到"Spin"按钮或类似的游戏按钮
        4. 点击Spin按钮开始游戏
        5. 等待游戏结果显示（大约30秒）
        6. 观察是否有任何奖励或结果显示
        7. 如果有弹窗或确认按钮，请适当处理
        
        注意：
        - 页面可能需要一些时间加载
        - Spin按钮可能有不同的样式或文字
        - 请耐心等待游戏动画完成
        - 如果遇到任何错误，请尝试刷新页面
        """
        
        print(f"浏览器 {browser_num} 创建AI Agent...")
        
        # 创建AI Agent
        agent = Agent(
            task=task_description,
            llm=llm,
            browser_session=browser_session,
            max_actions=25,  # 增加操作步数，应对复杂页面
            use_vision=True,  # 启用视觉理解，帮助识别按钮
        )
        
        print(f"浏览器 {browser_num} AI Agent开始执行Spekter任务...")
        
        # 执行任务
        result = await agent.run()
        
        print(f"浏览器 {browser_num} AI Agent任务执行完成")
        print(f"执行结果: {result}")
        
        # 额外等待，确保游戏完成
        print(f"浏览器 {browser_num} 额外等待30秒确保游戏完成...")
        await asyncio.sleep(30)
        
        # 保存最终截图
        try:
            page = await browser_session.get_current_page()
            screenshot_path = f"spekter_ai_final_browser_{browser_num}.png"
            await page.screenshot(path=screenshot_path)
            print(f"浏览器 {browser_num} Spekter AI任务截图已保存: {screenshot_path}")
        except Exception as e:
            print(f"保存截图失败: {e}")
        
        print(f"浏览器 {browser_num} Spekter AI Spin任务完成")
        
    except Exception as e:
        print(f"浏览器 {browser_num} Spekter AI任务失败: {e}")
        import traceback
        traceback.print_exc()

# 兼容性函数
async def run_automation_task(browser_session, browser_num):
    """兼容性函数"""
    await run_stealth_task(browser_session, browser_num)
