import asyncio
import os
import sys

# browser-use 0.4.5版本直接从已安装的库导入，不需要添加路径

from dotenv import load_dotenv

load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatGoogle

# 检查API密钥
api_key = os.getenv('GOOGLE_API_KEY')
if not api_key:
    raise ValueError('GOOGLE_API_KEY is not set. Please set your Google API key.')

# 初始化Gemini模型
llm = ChatGoogle(model='gemini-2.0-flash-exp', api_key=api_key)

# 创建浏览器会话
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        viewport_expansion=0,
        user_data_dir='E:\\newchrome\\网页缓存10',
        stealth=True,
        headless=False,
    )
)

async def run_google_search():
    """执行Google搜索任务"""
    
    print("🔍 开始执行Google搜索任务...")
    
    agent = Agent(
        task="""
        请执行以下Google搜索任务：
        
        1. 访问 https://www.google.com
        2. 在搜索框中输入 "browser-use github"
        3. 点击搜索按钮或按回车
        4. 等待搜索结果加载
        5. 点击第一个搜索结果
        6. 在打开的GitHub页面上，找到Star按钮并点击
        7. 截图保存最终结果
        
        注意：
        - 如果遇到Cookie同意弹窗，请点击接受
        - 如果需要登录GitHub，请告诉我
        - 操作要自然，等待页面完全加载
        """,
        llm=llm,
        max_actions_per_step=4,
        browser_session=browser_session,
    )
    
    print("🤖 AI代理开始执行搜索任务...")
    result = await agent.run(max_steps=25)
    
    print("✅ Google搜索任务执行完成!")
    print(f"执行结果: {result}")

if __name__ == '__main__':
    print("🚀 启动Google搜索自动化脚本...")
    print("📋 任务: 搜索browser-use项目并点星")
    print("🛡️ 模式: Stealth模式 (防检测)")
    print("🤖 AI模型: Gemini 2.0 Flash")
    print("-" * 50)
    
    asyncio.run(run_google_search())
