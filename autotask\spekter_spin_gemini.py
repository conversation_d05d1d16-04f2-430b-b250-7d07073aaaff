import asyncio
import os
import sys

# browser-use 0.4.5版本直接从已安装的库导入，不需要添加路径

from dotenv import load_dotenv

load_dotenv()

from browser_use import Agent
from browser_use.browser import BrowserProfile, BrowserSession
from browser_use.llm import ChatGoogle

# 检查API密钥
api_key = os.getenv('GOOGLE_API_KEY')
if not api_key:
    # 如果环境变量没有设置，直接在这里设置API密钥
    api_key = "AIzaSyBh-feefDQ35tKPgTSXf9dPClb3ZnKaL0c"  # 替换为您的真实API密钥

if not api_key:
    raise ValueError('GOOGLE_API_KEY is not set. Please set your Google API key.')

# 初始化Gemini模型
llm = ChatGoogle(model='gemini-2.0-flash-exp', api_key=api_key)

# 创建浏览器会话（使用stealth模式）
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        viewport_expansion=0,
        user_data_dir='E:\\newchrome\\网页缓存10',  # 可以修改为其他浏览器编号
        stealth=True,  # 启用stealth模式
        headless=False,  # 显示浏览器窗口
    )
)

async def run_spekter_spin():
    """执行Spekter游戏Spin任务"""
    
    print("🎮 开始执行Spekter游戏Spin任务...")
    
    agent = Agent(
        task="""
        请执行以下Spekter游戏任务：
        
        1. 访问 https://dispatch.spekter.games/spin 网站
        2. 等待页面完全加载
        3. 仔细观察页面，找到"Spin"按钮或类似的游戏按钮
        4. 点击Spin按钮开始游戏
        5. 等待游戏结果显示（大约30秒）
        6. 观察是否有任何奖励或结果显示
        7. 如果有弹窗或确认按钮，请适当处理
        
        注意：
        - 页面可能需要一些时间加载
        - Spin按钮可能有不同的样式或文字
        - 请耐心等待游戏动画完成
        - 如果遇到任何错误，请尝试刷新页面
        """,
        llm=llm,
        max_actions_per_step=4,
        browser_session=browser_session,
    )
    
    print("🤖 AI代理开始执行任务...")
    result = await agent.run(max_steps=25)
    
    print("✅ Spekter Spin任务执行完成!")
    print(f"执行结果: {result}")

# 全局任务控制变量
_task_cancelled = False
_current_agent = None

# API调用限制控制
_api_semaphore = asyncio.Semaphore(2)  # 最多同时2个API调用
_api_delay = 1.0  # API调用间隔（秒）

def cancel_task():
    """取消当前任务 - 通过修改任务目标让AI自然退出"""
    global _task_cancelled, _current_agent
    _task_cancelled = True
    print("🛑 任务取消信号已发送")

    # 如果有正在运行的Agent，修改其任务描述
    if _current_agent:
        try:
            # 修改Agent的任务描述，让它认为任务已完成
            _current_agent.task = """
            用户已请求停止任务。请立即完成当前操作并结束任务。

            请执行以下操作：
            1. 停止当前所有操作
            2. 截图保存当前页面状态
            3. 报告任务已按用户要求停止
            4. 立即结束任务

            重要：不要继续执行原始任务，用户已明确要求停止。
            """
            print("🔄 已修改AI任务目标，引导其自然退出")
        except Exception as e:
            print(f"修改AI任务目标失败: {e}")

# Chrome管理器集成函数
async def run_stealth_task(browser_session, browser_num):
    """
    Chrome管理器调用的stealth任务函数

    Args:
        browser_session: browser-use的BrowserSession对象
        browser_num: 浏览器编号
    """
    global _task_cancelled, _current_agent, _api_semaphore, _api_delay
    _task_cancelled = False  # 重置取消标志
    _current_agent = None

    # 使用信号量控制API并发数
    async with _api_semaphore:
        try:
            print(f"🎮 浏览器 {browser_num} 开始执行Spekter游戏Spin任务...")

            # 添加延迟，避免API调用过于频繁
            await asyncio.sleep(_api_delay * (browser_num - 1))
            print(f"⏱️ 浏览器 {browser_num} 等待 {_api_delay * (browser_num - 1):.1f}秒后开始")

            # 检查是否已取消
            if _task_cancelled:
                print(f"🛑 浏览器 {browser_num} 任务已被取消")
                return

        agent = Agent(
            task="""
            请执行以下Spekter游戏任务：

            1. 访问 https://dispatch.spekter.games/spin 网站
            2. 等待页面完全加载
            3. 仔细观察页面，找到"Spin"按钮或类似的游戏按钮
            4. 点击Spin按钮开始游戏
            5. 等待游戏结果显示（大约30秒）
            6. 观察是否有任何奖励或结果显示
            7. 如果有弹窗或确认按钮，请适当处理

            注意：
            - 页面可能需要一些时间加载
            - Spin按钮可能有不同的样式或文字
            - 请耐心等待游戏动画完成
            - 如果遇到任何错误，请尝试刷新页面
            - 如果用户要求停止，请立即结束任务
            """,
            llm=llm,
            max_actions_per_step=4,
            browser_session=browser_session,
        )

        # 保存Agent引用，用于取消时修改任务
        _current_agent = agent

        print(f"🤖 浏览器 {browser_num} AI代理开始执行任务...")

        # 创建任务执行和取消监控
        async def monitor_cancellation():
            """监控取消信号并修改任务"""
            while not _task_cancelled:
                await asyncio.sleep(1)  # 每秒检查一次

            # 如果被取消，修改Agent的任务描述
            if _current_agent:
                print(f"🔄 浏览器 {browser_num} 正在引导AI自然退出...")
                _current_agent.task = """
                用户已请求停止任务。请立即完成当前操作并结束任务。

                请执行以下操作：
                1. 停止当前所有操作
                2. 截图保存当前页面状态
                3. 报告任务已按用户要求停止
                4. 立即结束任务

                重要：不要继续执行原始任务，用户已明确要求停止。
                """

        # 同时运行Agent任务和取消监控
        agent_task = asyncio.create_task(agent.run(max_steps=10))
        monitor_task = asyncio.create_task(monitor_cancellation())

        # 等待任务完成或被取消
        done, pending = await asyncio.wait(
            [agent_task, monitor_task],
            return_when=asyncio.FIRST_COMPLETED
        )

        # 取消未完成的任务
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # 获取结果
        if agent_task in done:
            try:
                result = await agent_task
                if _task_cancelled:
                    print(f"🛑 浏览器 {browser_num} 任务被用户取消")
                else:
                    print(f"✅ 浏览器 {browser_num} Spekter Spin任务执行完成!")
            except Exception as e:
                result = f"任务执行出错: {e}"
        else:
            result = "任务被取消"
            print(f"执行结果: {result}")

        except Exception as e:
            print(f"❌ 浏览器 {browser_num} Spekter Spin任务失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            _current_agent = None



# 兼容性函数
async def run_automation_task(browser_session, browser_num):
    """兼容性函数，调用主要的stealth任务"""
    await run_stealth_task(browser_session, browser_num)

if __name__ == '__main__':
    print("🚀 启动Spekter游戏自动化脚本...")
    print("📋 任务: 访问Spekter游戏并执行Spin操作")
    print("🛡️ 模式: Stealth模式 (防检测)")
    print("🤖 AI模型: Gemini 2.0 Flash")
    print("-" * 50)

    asyncio.run(run_spekter_spin())
