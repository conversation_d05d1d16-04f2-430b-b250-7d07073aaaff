"""
AI任务配置文件
用于配置Gemini API和其他AI相关设置
"""
import os

class AIConfig:
    """AI配置类"""
    
    def __init__(self):
        self.gemini_api_key = None
        self.model_name = "gemini-1.5-flash"
        self.temperature = 0.1
        self.max_actions = 20
        self.use_vision = True
        
    def get_gemini_api_key(self):
        """获取Gemini API密钥"""
        if self.gemini_api_key:
            return self.gemini_api_key
            
        # 从环境变量获取
        api_key = os.getenv('GEMINI_API_KEY')
        if api_key:
            self.gemini_api_key = api_key
            return api_key
            
        # 从配置文件获取（如果存在）
        try:
            config_file = os.path.join(os.path.dirname(__file__), 'api_keys.txt')
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.startswith('GEMINI_API_KEY='):
                            api_key = line.split('=', 1)[1].strip()
                            self.gemini_api_key = api_key
                            return api_key
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            
        return None
    
    def set_gemini_api_key(self, api_key):
        """设置Gemini API密钥"""
        self.gemini_api_key = api_key
        
    def get_llm_config(self):
        """获取LLM配置"""
        return {
            'model': self.model_name,
            'api_key': self.get_gemini_api_key(),
            'temperature': self.temperature
        }
    
    def get_agent_config(self):
        """获取Agent配置"""
        return {
            'max_actions': self.max_actions,
            'use_vision': self.use_vision
        }

# 全局配置实例
ai_config = AIConfig()

def setup_gemini_api():
    """设置Gemini API密钥的辅助函数"""
    api_key = ai_config.get_gemini_api_key()
    
    if not api_key:
        print("=" * 50)
        print("Gemini API密钥设置")
        print("=" * 50)
        print("请选择设置方式:")
        print("1. 设置环境变量（推荐）")
        print("2. 创建配置文件")
        print("3. 临时输入（仅本次使用）")
        print()
        
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            print("\n请在命令行中设置环境变量:")
            print("Windows: set GEMINI_API_KEY=your_api_key_here")
            print("Linux/Mac: export GEMINI_API_KEY=your_api_key_here")
            print("\n设置后重新运行程序")
            
        elif choice == "2":
            api_key = input("\n请输入您的Gemini API密钥: ").strip()
            if api_key:
                try:
                    config_file = os.path.join(os.path.dirname(__file__), 'api_keys.txt')
                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write(f"GEMINI_API_KEY={api_key}\n")
                    print(f"API密钥已保存到: {config_file}")
                    ai_config.set_gemini_api_key(api_key)
                    return api_key
                except Exception as e:
                    print(f"保存配置文件失败: {e}")
                    
        elif choice == "3":
            api_key = input("\n请输入您的Gemini API密钥: ").strip()
            if api_key:
                ai_config.set_gemini_api_key(api_key)
                return api_key
        
        return None
    
    return api_key

"""
使用说明：

1. 获取Gemini API密钥：
   - 访问 https://makersuite.google.com/app/apikey
   - 创建新的API密钥
   - 复制密钥备用

2. 设置API密钥的三种方式：

   方式1：环境变量（推荐）
   Windows:
   set GEMINI_API_KEY=your_api_key_here
   
   Linux/Mac:
   export GEMINI_API_KEY=your_api_key_here

   方式2：配置文件
   在autotask目录创建 api_keys.txt 文件：
   GEMINI_API_KEY=your_api_key_here

   方式3：代码中直接设置
   from autotask.ai_config import ai_config
   ai_config.set_gemini_api_key("your_api_key_here")

3. 在任务中使用：
   from autotask.ai_config import ai_config, setup_gemini_api
   from browser_use.llm import ChatGoogle
   
   api_key = setup_gemini_api()
   if api_key:
       llm = ChatGoogle(**ai_config.get_llm_config())

4. 模型选择：
   - gemini-1.5-flash: 快速响应，适合简单任务
   - gemini-1.5-pro: 更强能力，适合复杂任务

5. 参数调整：
   - temperature: 0.0-1.0，控制随机性
   - max_actions: 最大操作步数
   - use_vision: 是否启用视觉理解
"""
