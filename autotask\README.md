# 自动化任务目录

这个目录用于存放所有的自动化任务脚本，使用browser-use + Gemini AI执行。

## 📁 目录结构

```
autotask/
├── README.md                    # 说明文档（本文件）
├── spekter_spin_gemini.py       # Spekter游戏Spin任务
├── custom_task_template.py      # 自定义任务模板
├── batch_spekter_spin.py        # 批量Spekter任务
├── google_search_task.py        # Google搜索任务
├── fingerprint_test.py          # 浏览器指纹检测
├── bot_detection_test.py        # 机器人检测测试
├── spekter_spin.py              # 原有的stealth任务
└── template_task.py             # 原有的任务模板
```

## 🚀 快速开始

### 1. 设置API密钥
```bash
# Windows
set GOOGLE_API_KEY=your_api_key_here

# Linux/Mac
export GOOGLE_API_KEY=your_api_key_here
```

### 2. 直接运行任务脚本
```bash
# 进入autotask目录
cd autotask

# 运行单个任务
python spekter_spin_gemini.py
python google_search_task.py
python fingerprint_test.py

# 运行批量任务
python batch_spekter_spin.py
```

### 3. 创建自定义任务
1. 复制 `custom_task_template.py` 文件
2. 重命名为您的任务名称（如：`my_game_task.py`）
3. 修改任务描述和参数
4. 直接运行脚本

### 4. 通过Chrome管理器使用
1. 将任务文件保存到此目录
2. 在Chrome管理器的自动化任务板块点击"刷新"按钮
3. 在下拉列表中选择您的任务（显示为 `[自定义] 任务名称`）
4. 输入浏览器编号并执行

## 📝 编写任务指南

### 基本结构
```python
import asyncio

async def run_stealth_task(browser_session, browser_num):
    try:
        # 1. 导航到目标页面
        await browser_session.create_new_tab("https://example.com")
        
        # 2. 获取页面对象
        page = await browser_session.get_current_page()
        
        # 3. 执行操作
        await page.click('button')
        await page.fill('input', 'text')
        
        # 4. 等待和截图
        await asyncio.sleep(5)
        await page.screenshot(path=f"result_{browser_num}.png")
        
    except Exception as e:
        print(f"任务失败: {e}")
```

### 常用操作

#### 页面导航
```python
# 在新标签页打开
await browser_session.create_new_tab("https://example.com")

# 获取当前页面
page = await browser_session.get_current_page()
```

#### 元素操作
```python
# 点击元素
await page.click('button')
await page.click('.class-name')
await page.click('#element-id')

# 填写表单
await page.fill('input[name="username"]', 'myusername')
await page.fill('textarea', 'long text content')

# 选择下拉框
await page.select_option('select', 'option-value')
```

#### 等待和延时
```python
# 简单等待
await asyncio.sleep(5)  # 等待5秒

# 等待元素出现
await page.wait_for_selector('button')

# 等待页面加载
await page.wait_for_load_state('networkidle')
```

#### 截图和调试
```python
# 保存截图
await page.screenshot(path=f"screenshot_{browser_num}.png")

# 获取页面内容
content = await page.content()
print(content)
```

### 选择器指南

#### CSS选择器
- 标签：`button`, `input`, `div`
- 类名：`.my-class`
- ID：`#my-id`
- 属性：`[data-testid="test"]`
- 组合：`div.class-name`

#### 文本选择器
- 包含文本：`button:has-text("点击")`
- 精确文本：`button:text("确定")`

#### 常用元素选择器
```python
# 按钮
'button'
'input[type="button"]'
'input[type="submit"]'
'.btn'

# 输入框
'input[type="text"]'
'input[type="password"]'
'input[name="username"]'
'textarea'

# 链接
'a'
'a[href*="keyword"]'
'a:has-text("链接文本")'
```

## 🎯 示例任务

### 简单点击任务
```python
async def run_stealth_task(browser_session, browser_num):
    await browser_session.create_new_tab("https://example.com")
    page = await browser_session.get_current_page()
    await page.click('button:has-text("开始")')
    await asyncio.sleep(10)
    await page.screenshot(path=f"result_{browser_num}.png")
```

### 表单填写任务
```python
async def run_stealth_task(browser_session, browser_num):
    await browser_session.create_new_tab("https://form.example.com")
    page = await browser_session.get_current_page()
    
    await page.fill('input[name="name"]', 'John Doe')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.click('button[type="submit"]')
    
    await asyncio.sleep(5)
    await page.screenshot(path=f"form_result_{browser_num}.png")
```

## 🔧 调试技巧

1. **使用截图调试**：在关键步骤保存截图
2. **打印日志**：使用 `print()` 输出调试信息
3. **异常处理**：用 try-except 包装可能失败的操作
4. **分步执行**：将复杂任务分解为小步骤

## ⚠️ 注意事项

1. 文件名只能包含字母、数字和下划线
2. 避免使用中文文件名
3. 确保异步函数使用 `async/await`
4. 处理可能的异常情况
5. 合理设置等待时间，避免过快操作被检测

## 🆘 常见问题

**Q: 任务不显示在列表中？**
A: 检查文件名是否正确，点击刷新按钮，确保文件包含必要的函数。

**Q: 找不到元素？**
A: 使用浏览器开发者工具检查元素选择器，尝试不同的选择器。

**Q: 任务执行失败？**
A: 查看控制台输出的错误信息，检查网络连接和页面加载情况。
