---
title: "Supported Models"
description: "Guide to using different chat models with <PERSON><PERSON><PERSON> Use"
icon: "robot"
---

## Overview

Here's how to configure the models.

### Migration from Langchain

We have recently switched from Langchain to our own implementation of the models. To migrate the previous code, just replace `from langchain_openai import ChatOpenAI` with `from browser_use.llm import ChatOpenAI` etc. The methods should be compatible(ish).

We also made and example [here](https://github.com/browser-use/browser-use/blob/main/examples/models/langchain) to help you stay with Langchain in case your workflow requires it.

## Model Recommendations

We recommend using GPT-4.1 for the best performance (best accuracy ~\$0.01 per step). The best price to performance can be achieved using `gemini-2.0-flash-exp` (currently also the most popular model, costs ~\$0.001 per step).

## Supported Models

Our library natively supports the following models:

- OpenAI
- Anthropic
- AWS Bedrock (multiple providers)
- Azure OpenAI
- Gemini
- Groq

We also support all other models that can be called via OpenAI compatible API (deepseek, novita, x, qwen). Please open a PR if you want to add a model.

We have natively switched to structured output when possible,

### OpenAI

OpenAI's GPT-4.1 models are recommended for best performance.

```python
from browser_use.llm import ChatOpenAI
from browser_use import Agent

# Initialize the model
llm = ChatOpenAI(
    model="gpt-4.1",
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)
```

Required environment variables:

```bash .env
OPENAI_API_KEY=
```

### Anthropic

```python
from browser_use.llm import ChatAnthropic
from browser_use import Agent

# Initialize the model
llm = ChatAnthropic(
    model="claude-3-5-sonnet-20240620",
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)
```

And add the variable:

```bash .env
ANTHROPIC_API_KEY=
```

### Azure OpenAI

```python
from browser_use.llm import ChatAzureOpenAI
from browser_use import Agent
from pydantic import SecretStr
import os

# Initialize the model
llm = ChatAzureOpenAI(
    model="gpt-4.1",
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)
```

Required environment variables:

```bash .env
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_API_KEY=
```

### Gemini

> [!IMPORTANT] `GEMINI_API_KEY` was the old environment var name, it should be called `GOOGLE_API_KEY` as of 2025-05.

```python
from browser_use.llm import ChatGoogle
from browser_use import Agent
from dotenv import load_dotenv

# Read GOOGLE_API_KEY into env
load_dotenv()

# Initialize the model
llm = ChatGoogle(model='gemini-2.0-flash-exp')

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)
```

Required environment variables:

```bash .env
GOOGLE_API_KEY=
```

### AWS Bedrock

AWS Bedrock provides access to multiple model providers through a single API. We support both a general AWS Bedrock client and provider-specific convenience classes.

#### General AWS Bedrock (supports all providers)

```python
from browser_use.llm import ChatAWSBedrock
from browser_use import Agent

# Works with any Bedrock model (Anthropic, Meta, AI21, etc.)
llm = ChatAWSBedrock(
    model="anthropic.claude-3-5-sonnet-20240620-v1:0",  # or any Bedrock model
    aws_region="us-east-1",
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)
```

#### Anthropic Claude via AWS Bedrock (convenience class)

```python
from browser_use.llm import ChatAnthropicBedrock
from browser_use import Agent

# Anthropic-specific class with Claude defaults
llm = ChatAnthropicBedrock(
    model="anthropic.claude-3-5-sonnet-20240620-v1:0",
    aws_region="us-east-1",
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)
```

#### AWS Authentication

Required environment variables:

```bash .env
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
```

You can also use AWS profiles or IAM roles instead of environment variables. The implementation supports:

- Environment variables (`AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_DEFAULT_REGION`)
- AWS profiles and credential files
- IAM roles (when running on EC2)
- Session tokens for temporary credentials
- AWS SSO authentication (`aws_sso_auth=True`)

## Groq

```python
from browser_use.llm import ChatGroq
from browser_use import Agent

llm = ChatGroq(model="meta-llama/llama-4-maverick-17b-128e-instruct")

agent = Agent(
    task="Your task here",
    llm=llm
)
```

Required environment variables:

```bash .env
GROQ_API_KEY=
```

## Ollama

```python
from browser_use.llm import ChatOllama
from browser_use import Agent

llm = ChatOllama(model="llama3.1:8b")
```
