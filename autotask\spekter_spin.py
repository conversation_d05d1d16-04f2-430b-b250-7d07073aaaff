"""
Spekter游戏自动Spin任务 - 自定义自动化任务
使用browser-use stealth模式执行
"""
import asyncio

async def run_stealth_task(browser_session, browser_num):
    """
    使用stealth模式运行Spekter游戏Spin任务
    
    Args:
        browser_session: browser-use的BrowserSession对象
        browser_num: 浏览器编号
    """
    try:
        target_url = "https://dispatch.spekter.games/spin"
        print(f"浏览器 {browser_num} 开始Spekter Spin任务: {target_url}")
        
        # 导航到Spekter游戏页面
        await browser_session.create_new_tab(target_url)
        print(f"浏览器 {browser_num} 已导航到Spekter游戏页面")
        
        # 等待页面加载
        await asyncio.sleep(5)
        
        # 获取当前页面
        page = await browser_session.get_current_page()
        
        # 尝试查找并点击Spin按钮
        print(f"浏览器 {browser_num} 正在查找Spin按钮...")
        
        # 尝试多种可能的Spin按钮选择器
        spin_selectors = [
            'button:has-text("spin")',
            'button:has-text("Spin")', 
            'button:has-text("SPIN")',
            '[data-testid="spin-button"]',
            '.spin-button',
            '#spin-button',
            'button[class*="spin"]',
            'div[class*="spin"]',
            'button'  # 最后尝试任何按钮
        ]
        
        spin_clicked = False
        for selector in spin_selectors:
            try:
                print(f"浏览器 {browser_num} 尝试选择器: {selector}")
                await page.click(selector, timeout=3000)
                print(f"浏览器 {browser_num} 成功点击Spin按钮 (选择器: {selector})")
                spin_clicked = True
                break
            except Exception as e:
                print(f"浏览器 {browser_num} 选择器 {selector} 失败: {e}")
                continue
        
        if not spin_clicked:
            print(f"浏览器 {browser_num} 未找到Spin按钮，保存页面截图")
            screenshot_path = f"spekter_no_button_browser_{browser_num}.png"
            await page.screenshot(path=screenshot_path)
            print(f"浏览器 {browser_num} 页面截图已保存: {screenshot_path}")
        
        # 等待30秒
        print(f"浏览器 {browser_num} 等待30秒...")
        for i in range(30):
            await asyncio.sleep(1)
            if i % 10 == 0:
                print(f"浏览器 {browser_num} 剩余等待时间: {30-i}秒")
        
        # 任务完成截图
        final_screenshot = f"spekter_final_browser_{browser_num}.png"
        await page.screenshot(path=final_screenshot)
        print(f"浏览器 {browser_num} Spekter任务完成截图已保存: {final_screenshot}")
        
        print(f"浏览器 {browser_num} Spekter Spin任务执行完成")
        
    except Exception as e:
        print(f"浏览器 {browser_num} Spekter Spin任务失败: {e}")
        import traceback
        traceback.print_exc()

# 兼容性函数（如果需要的话）
async def run_automation_task(browser_session, browser_num):
    """兼容性函数，调用主要的stealth任务"""
    await run_stealth_task(browser_session, browser_num)
